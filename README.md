# XWiki 水印扩展

[![License](https://img.shields.io/badge/License-LGPL%202.1-blue.svg)](https://www.gnu.org/licenses/lgpl-2.1)
[![XWiki Version](https://img.shields.io/badge/XWiki-17.4.3-green.svg)](https://www.xwiki.org/)
[![Java Version](https://img.shields.io/badge/Java-17+-orange.svg)](https://openjdk.java.net/)

专为 XWiki 17.4.3 设计的专业水印扩展，提供基于 Canvas 的高质量动态水印功能，支持占位符替换、防复制保护和移动端适配。

## ✨ 功能特性

### 🎨 动态水印渲染
- **Canvas 高质量渲染**：使用 HTML5 Canvas API 生成清晰的水印效果
- **占位符支持**：支持 `${user}`、`${timestamp}`、`${date}`、`${time}` 动态占位符
- **样式自定义**：可配置字体大小、透明度、旋转角度、间距等参数
- **平铺模式**：智能网格平铺算法，确保水印覆盖整个页面

### 🔒 内容保护
- **防复制功能**：可选的文本选择和复制保护
- **图片保护**：防止图片拖拽和右键保存
- **上下文菜单禁用**：阻止右键菜单访问

### 📱 移动端支持
- **响应式设计**：自动适配不同屏幕尺寸
- **移动端优化**：针对触摸设备的性能优化
- **可选启用**：可配置是否在移动设备上显示水印

### ⚙️ 管理功能
- **可视化配置**：集成到 XWiki 标准管理面板
- **实时预览**：配置界面提供水印效果预览
- **多语言支持**：中文和英文界面
- **配置验证**：完整的表单验证和错误提示

## 🚀 快速开始

### 系统要求

- **XWiki**: 17.4.3 或更高版本
- **Java**: 17 或更高版本
- **浏览器**: 支持 HTML5 Canvas 的现代浏览器

### 安装步骤

1. **下载扩展包**
   ```bash
   # 从 releases 页面下载最新的 JAR 文件
   wget https://github.com/your-repo/xwiki-extension-watermark/releases/latest/download/xwiki-extension-watermark-1.0-SNAPSHOT.jar
   ```

2. **安装到 XWiki**
   ```bash
   # 将 JAR 文件复制到 XWiki 的 WEB-INF/lib 目录
   cp xwiki-extension-watermark-1.0-SNAPSHOT.jar /path/to/xwiki/WEB-INF/lib/
   ```

3. **重启 XWiki**
   ```bash
   # 重启 XWiki 服务器以加载扩展
   systemctl restart xwiki  # 或其他重启命令
   ```

4. **验证安装**
   - 登录 XWiki 管理界面
   - 导航到 `管理 > 扩展` 查看是否已安装
   - 访问 `管理 > 水印配置` 进行配置

## 📖 使用指南

### 基本配置

1. **访问配置页面**
   - 以管理员身份登录 XWiki
   - 导航到 `管理 > 水印配置`

2. **启用水印**
   - 勾选"启用水印"选项
   - 点击"保存配置"

3. **配置文本模板**
   ```
   # 基本模板
   ${user} - ${timestamp}
   
   # 自定义模板示例
   机密文档 - ${user} - ${date}
   版权所有 © 2025 - ${user}
   ```

### 高级配置

#### 样式参数

| 参数 | 说明 | 默认值 | 范围 |
|------|------|--------|------|
| 字体大小 | 水印文字大小 | 16px | 8-72px |
| 透明度 | 水印透明度 | 0.1 | 0.01-1.0 |
| 旋转角度 | 文字旋转角度 | -15° | -90°到90° |
| 水平间距 | 水印水平间距 | 200px | 50-500px |
| 垂直间距 | 水印垂直间距 | 150px | 50-500px |

#### 占位符说明

| 占位符 | 说明 | 示例输出 |
|--------|------|----------|
| `${user}` | 当前用户名 | `admin` |
| `${timestamp}` | 完整时间戳 | `2025-01-13 23:30:45` |
| `${date}` | 当前日期 | `2025-01-13` |
| `${time}` | 当前时间 | `23:30:45` |

### 防复制功能

启用防复制功能后，页面将：
- 禁用文本选择
- 禁用右键菜单
- 禁用图片拖拽
- 禁用键盘快捷键复制

**注意**：防复制功能可能影响正常的页面交互，请根据需要谨慎启用。

## 🔧 开发指南

### 构建项目

```bash
# 克隆项目
git clone https://github.com/your-repo/xwiki-extension-watermark.git
cd xwiki-extension-watermark

# 构建项目
./gradlew build

# 运行测试
./gradlew test

# 生成 JAR 文件
./gradlew jar
```

### 项目结构

```
src/
├── main/
│   ├── java/
│   │   └── com/microcredchina/xwikiwatermark/
│   │       ├── WatermarkService.java              # 核心服务接口
│   │       ├── WatermarkConfig.java               # 配置数据类
│   │       └── internal/
│   │           ├── DefaultWatermarkService.java   # 默认服务实现
│   │           └── WatermarkScriptService.java    # 脚本服务
│   └── resources/
│       ├── META-INF/
│       │   └── components.txt                     # 组件注册
│       ├── watermark/
│       │   ├── watermark.js                       # 前端渲染器
│       │   ├── watermark.css                      # 样式文件
│       │   └── admin-config.vm                    # 管理界面
│       ├── watermark_en.properties                # 英文翻译
│       └── watermark_zh.properties                # 中文翻译
└── test/
    └── java/
        └── com/microcredchina/xwikiwatermark/
            └── DefaultWatermarkServiceTest.java   # 单元测试
```

### API 使用

#### Java API

```java
// 获取水印服务
WatermarkService watermarkService = componentManager.getInstance(WatermarkService.class);

// 获取配置
WatermarkConfig config = watermarkService.getConfiguration();

// 处理模板
String processedText = watermarkService.processTemplate("${user} - ${timestamp}");

// 检查启用状态
boolean enabled = watermarkService.isEnabled();
```

#### JavaScript API

```javascript
// 访问脚本服务
var watermarkService = $services.watermark;

// 获取配置
var config = watermarkService.getConfig();

// 处理文本
var processedText = watermarkService.processText("${user} - ${timestamp}");

// 刷新水印
if (window.XWikiWatermark) {
    window.XWikiWatermark.refresh();
}
```

## 🐛 故障排除

### 常见问题

#### 1. 水印不显示

**可能原因**：
- 水印功能未启用
- 浏览器不支持 Canvas
- JavaScript 被禁用

**解决方案**：
```bash
# 检查配置
curl -X GET "http://your-xwiki/xwiki/rest/wikis/xwiki/spaces/XWiki/pages/XWikiPreferences"

# 检查浏览器控制台是否有错误
# 确保启用了 JavaScript
```

#### 2. 管理界面无法访问

**可能原因**：
- 权限不足
- 扩展未正确安装

**解决方案**：
```bash
# 检查用户权限
# 确保以管理员身份登录

# 检查扩展是否安装
ls -la /path/to/xwiki/WEB-INF/lib/xwiki-extension-watermark*

# 检查日志文件
tail -f /path/to/xwiki/logs/xwiki.log
```

#### 3. 占位符不被替换

**可能原因**：
- 占位符语法错误
- 用户信息获取失败

**解决方案**：
```javascript
// 检查占位符语法
// 正确：${user}
// 错误：{user} 或 $user

// 测试脚本服务
console.log($services.watermark.getCurrentUser());
```

### 性能优化

#### 1. 减少重绘频率

```javascript
// 调整防抖延迟
XWikiWatermark.DEBOUNCE_DELAY = 500; // 增加到 500ms
```

#### 2. 移动端优化

```css
/* 在移动端禁用水印 */
@media (max-width: 768px) {
    #xwiki-watermark-container {
        display: none !important;
    }
}
```

## 📝 更新日志

### v1.0.0 (2025-01-13)

#### 新功能
- ✨ 基于 Canvas 的高质量水印渲染
- ✨ 动态占位符支持（${user}、${timestamp}、${date}、${time}）
- ✨ 可视化管理界面集成
- ✨ 防复制保护功能
- ✨ 移动端响应式支持
- ✨ 中英文双语界面

#### 技术特性
- 🔧 XWiki 17.4.3 兼容性
- 🔧 Java 17 支持
- 🔧 Gradle 构建系统
- 🔧 完整的单元测试覆盖
- 🔧 线程安全的配置缓存

## 🤝 贡献指南

我们欢迎社区贡献！请遵循以下步骤：

1. **Fork 项目**
2. **创建功能分支** (`git checkout -b feature/AmazingFeature`)
3. **提交更改** (`git commit -m 'Add some AmazingFeature'`)
4. **推送分支** (`git push origin feature/AmazingFeature`)
5. **创建 Pull Request**

### 代码规范

- 遵循 Java 编码规范
- 添加适当的单元测试
- 更新相关文档
- 确保所有测试通过

## 📄 许可证

本项目采用 [LGPL 2.1](https://www.gnu.org/licenses/lgpl-2.1) 许可证。详见 [LICENSE](LICENSE) 文件。

## 🙏 致谢

- [XWiki](https://www.xwiki.org/) - 优秀的企业级 Wiki 平台
- [Canvas API](https://developer.mozilla.org/en-US/docs/Web/API/Canvas_API) - 强大的图形渲染接口
- 所有贡献者和用户的支持

## 📞 支持

如果您遇到问题或有建议，请：

- 📋 [提交 Issue](https://github.com/your-repo/xwiki-extension-watermark/issues)
- 💬 [参与讨论](https://github.com/your-repo/xwiki-extension-watermark/discussions)
- 📧 发送邮件至：<EMAIL>

---

**MicroCred China** © 2025 - 专业的 XWiki 扩展开发
