plugins {
    id 'java'
}

group = 'com.microcredchina.xwikiwatermark'
version = '1.0-SNAPSHOT'

// Java 17 compatibility
java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

repositories {
    mavenLocal()
    mavenCentral()
    // XWiki Maven repositories as specified in requirements
    maven {
        url 'https://nexus.xwiki.org/nexus/content/groups/public/'
    }
    maven {
        url 'https://maven.xwiki.org/releases/'
    }
}

dependencies {
    // XWiki core dependencies - using compileOnly as specified in requirements
    // XWiki provides these at runtime
    compileOnly 'org.xwiki.commons:xwiki-commons-component-api:17.4.3'
    compileOnly 'org.xwiki.commons:xwiki-commons-script:17.4.3'
    compileOnly 'org.xwiki.platform:xwiki-platform-oldcore:17.4.3'
    compileOnly 'org.xwiki.commons:xwiki-commons-configuration-api:17.4.3'
    compileOnly 'org.xwiki.platform:xwiki-platform-bridge:17.4.3'

    // Test dependencies
    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation 'org.mockito:mockito-core:5.7.0'
    testImplementation 'org.mockito:mockito-junit-jupiter:5.7.0'

    // XWiki dependencies for testing (use testImplementation instead of compileOnly)
    testImplementation 'org.xwiki.commons:xwiki-commons-component-api:17.4.3'
    testImplementation 'org.xwiki.commons:xwiki-commons-script:17.4.3'
    testImplementation 'org.xwiki.platform:xwiki-platform-oldcore:17.4.3'
    testImplementation 'org.xwiki.commons:xwiki-commons-configuration-api:17.4.3'
    testImplementation 'org.xwiki.platform:xwiki-platform-bridge:17.4.3'
}

test {
    useJUnitPlatform()
}

// Ensure proper encoding for Chinese characters
tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
}

// JAR configuration for XWiki extension
jar {
    archiveBaseName = 'xwiki-extension-watermark'
    manifest {
        attributes(
            'Implementation-Title': 'XWiki Watermark Extension',
            'Implementation-Version': project.version,
            'Implementation-Vendor': 'MicroCred China'
        )
    }
}