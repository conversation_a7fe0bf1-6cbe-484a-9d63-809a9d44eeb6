/*
 * See the NOTICE file distributed with this work for additional
 * information regarding copyright ownership.
 *
 * This is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as
 * published by the Free Software Foundation; either version 2.1 of
 * the License, or (at your option) any later version.
 *
 * This software is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this software; if not, write to the Free
 * Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA
 * 02110-1301 USA, or see the FSF site: http://www.fsf.org.
 */
package com.microcredchina.xwikiwatermark;

import org.xwiki.stability.Unstable;

/**
 * 水印配置数据类，包含所有水印相关的配置参数。
 * 
 * 该类封装了水印功能的所有配置选项，包括：
 * - 基本设置（启用状态、文本模板）
 * - 样式参数（透明度、角度、字体大小、间距）
 * - 功能选项（防复制、移动端适配）
 * 
 * 所有配置参数都有合理的默认值，确保在没有明确配置时也能正常工作。
 * 
 * @version $Id$
 * @since 1.0
 */
@Unstable
public class WatermarkConfig
{
    /**
     * 默认文本模板。
     */
    public static final String DEFAULT_TEXT_TEMPLATE = "${user} - ${timestamp}";

    /**
     * 默认水平间距（像素）。
     */
    public static final int DEFAULT_X_SPACING = 200;

    /**
     * 默认垂直间距（像素）。
     */
    public static final int DEFAULT_Y_SPACING = 150;

    /**
     * 默认旋转角度（度）。
     */
    public static final int DEFAULT_ANGLE = -15;

    /**
     * 默认透明度（0.0-1.0）。
     */
    public static final double DEFAULT_OPACITY = 0.1;

    /**
     * 默认字体大小（像素）。
     */
    public static final int DEFAULT_FONT_SIZE = 16;

    private boolean enabled = false;
    private String textTemplate = DEFAULT_TEXT_TEMPLATE;
    private int xSpacing = DEFAULT_X_SPACING;
    private int ySpacing = DEFAULT_Y_SPACING;
    private int angle = DEFAULT_ANGLE;
    private double opacity = DEFAULT_OPACITY;
    private int fontSize = DEFAULT_FONT_SIZE;
    private boolean antiCopy = false;
    private boolean applyToMobile = true;

    /**
     * 默认构造函数，使用默认配置值。
     */
    public WatermarkConfig()
    {
        // 使用默认值
    }

    /**
     * 完整构造函数。
     * 
     * @param enabled 是否启用水印
     * @param textTemplate 文本模板
     * @param xSpacing 水平间距
     * @param ySpacing 垂直间距
     * @param angle 旋转角度
     * @param opacity 透明度
     * @param fontSize 字体大小
     * @param antiCopy 是否启用防复制
     * @param applyToMobile 是否应用到移动端
     */
    public WatermarkConfig(boolean enabled, String textTemplate, int xSpacing, int ySpacing,
        int angle, double opacity, int fontSize, boolean antiCopy, boolean applyToMobile)
    {
        this.enabled = enabled;
        this.textTemplate = textTemplate != null ? textTemplate : DEFAULT_TEXT_TEMPLATE;
        this.xSpacing = xSpacing > 0 ? xSpacing : DEFAULT_X_SPACING;
        this.ySpacing = ySpacing > 0 ? ySpacing : DEFAULT_Y_SPACING;
        this.angle = angle;
        this.opacity = Math.max(0.0, Math.min(1.0, opacity));
        this.fontSize = fontSize > 0 ? fontSize : DEFAULT_FONT_SIZE;
        this.antiCopy = antiCopy;
        this.applyToMobile = applyToMobile;
    }

    // Getter 方法

    /**
     * @return 是否启用水印功能
     */
    public boolean isEnabled()
    {
        return enabled;
    }

    /**
     * @return 文本模板
     */
    public String getTextTemplate()
    {
        return textTemplate;
    }

    /**
     * @return 水平间距（像素）
     */
    public int getXSpacing()
    {
        return xSpacing;
    }

    /**
     * @return 垂直间距（像素）
     */
    public int getYSpacing()
    {
        return ySpacing;
    }

    /**
     * @return 旋转角度（度）
     */
    public int getAngle()
    {
        return angle;
    }

    /**
     * @return 透明度（0.0-1.0）
     */
    public double getOpacity()
    {
        return opacity;
    }

    /**
     * @return 字体大小（像素）
     */
    public int getFontSize()
    {
        return fontSize;
    }

    /**
     * @return 是否启用防复制功能
     */
    public boolean isAntiCopy()
    {
        return antiCopy;
    }

    /**
     * @return 是否应用到移动端
     */
    public boolean isApplyToMobile()
    {
        return applyToMobile;
    }

    // Setter 方法

    /**
     * 设置是否启用水印功能。
     * 
     * @param enabled 是否启用
     */
    public void setEnabled(boolean enabled)
    {
        this.enabled = enabled;
    }

    /**
     * 设置文本模板。
     * 
     * @param textTemplate 文本模板，不能为null
     */
    public void setTextTemplate(String textTemplate)
    {
        this.textTemplate = textTemplate != null ? textTemplate : DEFAULT_TEXT_TEMPLATE;
    }

    /**
     * 设置水平间距。
     * 
     * @param xSpacing 水平间距，必须大于0
     */
    public void setXSpacing(int xSpacing)
    {
        this.xSpacing = xSpacing > 0 ? xSpacing : DEFAULT_X_SPACING;
    }

    /**
     * 设置垂直间距。
     * 
     * @param ySpacing 垂直间距，必须大于0
     */
    public void setYSpacing(int ySpacing)
    {
        this.ySpacing = ySpacing > 0 ? ySpacing : DEFAULT_Y_SPACING;
    }

    /**
     * 设置旋转角度。
     * 
     * @param angle 旋转角度（度）
     */
    public void setAngle(int angle)
    {
        this.angle = angle;
    }

    /**
     * 设置透明度。
     * 
     * @param opacity 透明度，自动限制在0.0-1.0范围内
     */
    public void setOpacity(double opacity)
    {
        this.opacity = Math.max(0.0, Math.min(1.0, opacity));
    }

    /**
     * 设置字体大小。
     * 
     * @param fontSize 字体大小，必须大于0
     */
    public void setFontSize(int fontSize)
    {
        this.fontSize = fontSize > 0 ? fontSize : DEFAULT_FONT_SIZE;
    }

    /**
     * 设置是否启用防复制功能。
     * 
     * @param antiCopy 是否启用防复制
     */
    public void setAntiCopy(boolean antiCopy)
    {
        this.antiCopy = antiCopy;
    }

    /**
     * 设置是否应用到移动端。
     * 
     * @param applyToMobile 是否应用到移动端
     */
    public void setApplyToMobile(boolean applyToMobile)
    {
        this.applyToMobile = applyToMobile;
    }

    @Override
    public String toString()
    {
        return String.format(
            "WatermarkConfig{enabled=%s, textTemplate='%s', xSpacing=%d, ySpacing=%d, " +
            "angle=%d, opacity=%.2f, fontSize=%d, antiCopy=%s, applyToMobile=%s}",
            enabled, textTemplate, xSpacing, ySpacing, angle, opacity, fontSize, antiCopy, applyToMobile
        );
    }
}
