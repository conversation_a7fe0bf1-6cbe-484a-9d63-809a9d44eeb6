/*
 * See the NOTICE file distributed with this work for additional
 * information regarding copyright ownership.
 *
 * This is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as
 * published by the Free Software Foundation; either version 2.1 of
 * the License, or (at your option) any later version.
 *
 * This software is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this software; if not, write to the Free
 * Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA
 * 02110-1301 USA, or see the FSF site: http://www.fsf.org.
 */
package com.microcredchina.xwikiwatermark;

import org.xwiki.component.annotation.Role;
import org.xwiki.stability.Unstable;

/**
 * 水印服务核心接口，提供水印功能的主要API。
 * 
 * 该服务负责：
 * - 获取和管理水印配置
 * - 处理占位符替换（${user}、${timestamp}等）
 * - 控制水印功能的启用状态
 * - 提供线程安全的配置访问
 * 
 * @version $Id$
 * @since 1.0
 */
@Role
@Unstable
public interface WatermarkService
{
    /**
     * 获取当前的水印配置。
     * 
     * 该方法返回包含所有水印配置参数的对象，包括：
     * - 启用状态
     * - 文本模板
     * - 样式参数（透明度、角度、间距等）
     * - 防复制设置
     * - 移动端适配设置
     * 
     * @return 当前的水印配置，如果配置不存在则返回默认配置
     */
    WatermarkConfig getConfiguration();

    /**
     * 处理文本模板中的占位符。
     * 
     * 支持的占位符：
     * - ${user}: 当前用户名
     * - ${timestamp}: 当前时间戳
     * - ${date}: 当前日期
     * - ${time}: 当前时间
     * 
     * @param template 包含占位符的文本模板
     * @return 处理后的文本，所有占位符都被替换为实际值
     * @throws IllegalArgumentException 如果模板为null或包含不支持的占位符
     */
    String processTemplate(String template);

    /**
     * 检查水印功能是否启用。
     * 
     * 该方法检查全局配置中的启用状态，同时考虑：
     * - 管理员配置的启用状态
     * - 当前用户的权限
     * - 系统资源状态
     * 
     * @return true 如果水印功能已启用，false 否则
     */
    boolean isEnabled();

    /**
     * 检查当前环境是否为移动端。
     * 
     * 该方法用于判断是否需要应用移动端特定的水印配置。
     * 
     * @return true 如果当前环境为移动端，false 否则
     */
    boolean isMobileEnvironment();

    /**
     * 获取当前用户的显示名称。
     * 
     * 该方法用于占位符替换，返回适合显示的用户名。
     * 
     * @return 当前用户的显示名称，如果用户未登录则返回"Guest"
     */
    String getCurrentUserDisplayName();

    /**
     * 刷新配置缓存。
     * 
     * 该方法强制重新加载配置，用于配置更新后的立即生效。
     * 通常在管理界面保存配置后调用。
     */
    void refreshConfiguration();
}
