/*
 * See the NOTICE file distributed with this work for additional
 * information regarding copyright ownership.
 *
 * This is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as
 * published by the Free Software Foundation; either version 2.1 of
 * the License, or (at your option) any later version.
 *
 * This software is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this software; if not, write to the Free
 * Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA
 * 02110-1301 USA, or see the FSF site: http://www.fsf.org.
 */
package com.microcredchina.xwikiwatermark.internal;

import java.util.HashMap;
import java.util.Map;

import javax.inject.Inject;
import javax.inject.Named;
import javax.inject.Singleton;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.xwiki.component.annotation.Component;
import org.xwiki.script.service.ScriptService;
import org.xwiki.stability.Unstable;

import com.microcredchina.xwikiwatermark.WatermarkConfig;
import com.microcredchina.xwikiwatermark.WatermarkService;

/**
 * 水印脚本服务，将水印功能暴露给前端JavaScript。
 * 
 * 该服务作为前后端的桥梁，提供：
 * - 配置信息的安全访问
 * - 文本处理功能
 * - 状态检查方法
 * - JSON格式的数据返回
 * 
 * 前端可以通过 $services.watermark 访问该服务。
 * 
 * @version $Id$
 * @since 1.0
 */
@Component
@Named("watermark")
@Singleton
@Unstable
public class WatermarkScriptService implements ScriptService
{
    private static final Logger LOGGER = LoggerFactory.getLogger(WatermarkScriptService.class);

    /**
     * 最大文本长度，防止过长的文本导致性能问题。
     */
    private static final int MAX_TEXT_LENGTH = 1000;

    @Inject
    private WatermarkService watermarkService;

    /**
     * 获取水印配置信息。
     * 
     * 返回适合前端JavaScript使用的配置对象，包含所有必要的配置参数。
     * 敏感信息会被过滤或转换为安全格式。
     * 
     * @return 包含配置信息的Map对象，如果获取失败则返回空Map
     */
    public Map<String, Object> getConfig()
    {
        Map<String, Object> result = new HashMap<>();
        
        try {
            WatermarkConfig config = watermarkService.getConfiguration();
            
            result.put("enabled", config.isEnabled());
            result.put("textTemplate", sanitizeText(config.getTextTemplate()));
            result.put("xSpacing", config.getXSpacing());
            result.put("ySpacing", config.getYSpacing());
            result.put("angle", config.getAngle());
            result.put("opacity", config.getOpacity());
            result.put("fontSize", config.getFontSize());
            result.put("antiCopy", config.isAntiCopy());
            result.put("applyToMobile", config.isApplyToMobile());
            
            LOGGER.debug("Configuration retrieved for frontend: enabled={}", config.isEnabled());
            
        } catch (Exception e) {
            LOGGER.error("Failed to get watermark configuration for frontend", e);
            // 返回安全的默认配置
            result.put("enabled", false);
            result.put("textTemplate", "");
            result.put("xSpacing", WatermarkConfig.DEFAULT_X_SPACING);
            result.put("ySpacing", WatermarkConfig.DEFAULT_Y_SPACING);
            result.put("angle", WatermarkConfig.DEFAULT_ANGLE);
            result.put("opacity", WatermarkConfig.DEFAULT_OPACITY);
            result.put("fontSize", WatermarkConfig.DEFAULT_FONT_SIZE);
            result.put("antiCopy", false);
            result.put("applyToMobile", true);
        }
        
        return result;
    }

    /**
     * 检查水印功能是否启用。
     * 
     * @return true 如果水印功能已启用，false 否则
     */
    public boolean isEnabled()
    {
        try {
            return watermarkService.isEnabled();
        } catch (Exception e) {
            LOGGER.error("Failed to check if watermark is enabled", e);
            return false;
        }
    }

    /**
     * 处理文本模板，替换其中的占位符。
     * 
     * 该方法对输入进行安全检查，防止XSS攻击和过长文本。
     * 
     * @param template 要处理的文本模板
     * @return 处理后的文本，如果处理失败则返回空字符串
     */
    public String processText(String template)
    {
        if (StringUtils.isBlank(template)) {
            return "";
        }

        // 安全检查：限制文本长度
        if (template.length() > MAX_TEXT_LENGTH) {
            LOGGER.warn("Text template too long: {} characters, truncating to {}", 
                template.length(), MAX_TEXT_LENGTH);
            template = template.substring(0, MAX_TEXT_LENGTH);
        }

        try {
            // 清理输入文本，防止XSS攻击
            String sanitizedTemplate = sanitizeText(template);
            return watermarkService.processTemplate(sanitizedTemplate);
        } catch (Exception e) {
            LOGGER.error("Failed to process text template: {}", template, e);
            return "";
        }
    }

    /**
     * 检查当前环境是否为移动端。
     * 
     * @return true 如果当前环境为移动端，false 否则
     */
    public boolean isMobileEnvironment()
    {
        try {
            return watermarkService.isMobileEnvironment();
        } catch (Exception e) {
            LOGGER.error("Failed to check mobile environment", e);
            return false;
        }
    }

    /**
     * 获取当前用户的显示名称。
     * 
     * @return 当前用户的显示名称
     */
    public String getCurrentUser()
    {
        try {
            return sanitizeText(watermarkService.getCurrentUserDisplayName());
        } catch (Exception e) {
            LOGGER.error("Failed to get current user", e);
            return "Guest";
        }
    }

    /**
     * 刷新配置缓存。
     * 
     * 该方法通常在管理界面保存配置后调用，确保配置立即生效。
     * 
     * @return true 如果刷新成功，false 否则
     */
    public boolean refreshConfiguration()
    {
        try {
            watermarkService.refreshConfiguration();
            LOGGER.info("Configuration cache refreshed via script service");
            return true;
        } catch (Exception e) {
            LOGGER.error("Failed to refresh configuration", e);
            return false;
        }
    }

    /**
     * 获取水印服务的状态信息。
     * 
     * 返回包含服务状态的详细信息，用于调试和监控。
     * 
     * @return 包含状态信息的Map对象
     */
    public Map<String, Object> getStatus()
    {
        Map<String, Object> status = new HashMap<>();
        
        try {
            status.put("serviceAvailable", true);
            status.put("enabled", watermarkService.isEnabled());
            status.put("currentUser", sanitizeText(watermarkService.getCurrentUserDisplayName()));
            status.put("mobileEnvironment", watermarkService.isMobileEnvironment());
            status.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            LOGGER.error("Failed to get watermark service status", e);
            status.put("serviceAvailable", false);
            status.put("error", "Service unavailable");
        }
        
        return status;
    }

    /**
     * 清理文本，防止XSS攻击。
     * 
     * @param text 要清理的文本
     * @return 清理后的安全文本
     */
    private String sanitizeText(String text)
    {
        if (text == null) {
            return "";
        }
        
        // 基本的XSS防护：移除HTML标签和脚本
        return text.replaceAll("<[^>]*>", "")
                  .replaceAll("javascript:", "")
                  .replaceAll("vbscript:", "")
                  .replaceAll("onload", "")
                  .replaceAll("onerror", "")
                  .replaceAll("onclick", "")
                  .trim();
    }
}
