/*
 * See the NOTICE file distributed with this work for additional
 * information regarding copyright ownership.
 *
 * This is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as
 * published by the Free Software Foundation; either version 2.1 of
 * the License, or (at your option) any later version.
 *
 * This software is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this software; if not, write to the Free
 * Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA
 * 02110-1301 USA, or see the FSF site: http://www.fsf.org.
 */
package com.microcredchina.xwikiwatermark.internal;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.inject.Inject;
import javax.inject.Singleton;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.xwiki.bridge.DocumentAccessBridge;
import org.xwiki.component.annotation.Component;
import org.xwiki.configuration.ConfigurationSource;
import org.xwiki.context.Execution;
import org.xwiki.context.ExecutionContext;
import org.xwiki.model.reference.DocumentReference;
import org.xwiki.model.reference.EntityReferenceSerializer;

import com.microcredchina.xwikiwatermark.WatermarkConfig;
import com.microcredchina.xwikiwatermark.WatermarkService;

/**
 * 默认水印服务实现。
 * 
 * 该实现提供：
 * - 基于XWikiPreferences的配置管理
 * - 线程安全的配置缓存
 * - 占位符替换功能
 * - 用户和环境检测
 * 
 * @version $Id$
 * @since 1.0
 */
@Component
@Singleton
public class DefaultWatermarkService implements WatermarkService
{
    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultWatermarkService.class);

    /**
     * 配置键前缀。
     */
    private static final String CONFIG_PREFIX = "watermark.";

    /**
     * 占位符模式。
     */
    private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");

    /**
     * 日期格式。
     */
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * 时间格式。
     */
    private static final SimpleDateFormat TIME_FORMAT = new SimpleDateFormat("HH:mm:ss");

    /**
     * 时间戳格式。
     */
    private static final SimpleDateFormat TIMESTAMP_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Inject
    private ConfigurationSource configurationSource;

    @Inject
    private DocumentAccessBridge documentAccessBridge;

    @Inject
    private Execution execution;

    @Inject
    private EntityReferenceSerializer<String> entityReferenceSerializer;

    /**
     * 配置缓存。
     */
    private volatile WatermarkConfig cachedConfig;

    /**
     * 配置缓存时间戳。
     */
    private volatile long cacheTimestamp;

    /**
     * 缓存有效期（毫秒）。
     */
    private static final long CACHE_VALIDITY_PERIOD = 30000; // 30秒

    /**
     * 读写锁，保护配置缓存。
     */
    private final ReadWriteLock cacheLock = new ReentrantReadWriteLock();

    @Override
    public WatermarkConfig getConfiguration()
    {
        cacheLock.readLock().lock();
        try {
            // 检查缓存是否有效
            if (cachedConfig != null && (System.currentTimeMillis() - cacheTimestamp) < CACHE_VALIDITY_PERIOD) {
                return cachedConfig;
            }
        } finally {
            cacheLock.readLock().unlock();
        }

        // 缓存无效，需要重新加载
        cacheLock.writeLock().lock();
        try {
            // 双重检查，避免重复加载
            if (cachedConfig == null || (System.currentTimeMillis() - cacheTimestamp) >= CACHE_VALIDITY_PERIOD) {
                cachedConfig = loadConfiguration();
                cacheTimestamp = System.currentTimeMillis();
                LOGGER.debug("Configuration reloaded: {}", cachedConfig);
            }
            return cachedConfig;
        } finally {
            cacheLock.writeLock().unlock();
        }
    }

    /**
     * 从配置源加载配置。
     * 
     * @return 加载的配置对象
     */
    private WatermarkConfig loadConfiguration()
    {
        try {
            boolean enabled = configurationSource.getProperty(CONFIG_PREFIX + "enabled", false);
            String textTemplate = configurationSource.getProperty(CONFIG_PREFIX + "textTemplate", 
                WatermarkConfig.DEFAULT_TEXT_TEMPLATE);
            int xSpacing = configurationSource.getProperty(CONFIG_PREFIX + "xSpacing", 
                WatermarkConfig.DEFAULT_X_SPACING);
            int ySpacing = configurationSource.getProperty(CONFIG_PREFIX + "ySpacing", 
                WatermarkConfig.DEFAULT_Y_SPACING);
            int angle = configurationSource.getProperty(CONFIG_PREFIX + "angle", 
                WatermarkConfig.DEFAULT_ANGLE);
            double opacity = configurationSource.getProperty(CONFIG_PREFIX + "opacity", 
                WatermarkConfig.DEFAULT_OPACITY);
            int fontSize = configurationSource.getProperty(CONFIG_PREFIX + "fontSize", 
                WatermarkConfig.DEFAULT_FONT_SIZE);
            boolean antiCopy = configurationSource.getProperty(CONFIG_PREFIX + "antiCopy", false);
            boolean applyToMobile = configurationSource.getProperty(CONFIG_PREFIX + "applyToMobile", true);

            return new WatermarkConfig(enabled, textTemplate, xSpacing, ySpacing, 
                angle, opacity, fontSize, antiCopy, applyToMobile);
        } catch (Exception e) {
            LOGGER.warn("Failed to load watermark configuration, using defaults", e);
            return new WatermarkConfig();
        }
    }

    @Override
    public String processTemplate(String template)
    {
        if (template == null) {
            throw new IllegalArgumentException("Template cannot be null");
        }

        try {
            Matcher matcher = PLACEHOLDER_PATTERN.matcher(template);
            StringBuffer result = new StringBuffer();

            while (matcher.find()) {
                String placeholder = matcher.group(1);
                String replacement = getPlaceholderValue(placeholder);
                matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
            }
            matcher.appendTail(result);

            return result.toString();
        } catch (Exception e) {
            LOGGER.error("Failed to process template: {}", template, e);
            return template; // 返回原始模板作为降级方案
        }
    }

    /**
     * 获取占位符的值。
     * 
     * @param placeholder 占位符名称
     * @return 占位符对应的值
     */
    private String getPlaceholderValue(String placeholder)
    {
        Date now = new Date();
        
        switch (placeholder.toLowerCase()) {
            case "user":
                return getCurrentUserDisplayName();
            case "timestamp":
                return TIMESTAMP_FORMAT.format(now);
            case "date":
                return DATE_FORMAT.format(now);
            case "time":
                return TIME_FORMAT.format(now);
            default:
                LOGGER.warn("Unknown placeholder: {}", placeholder);
                return "${" + placeholder + "}"; // 保留未知占位符
        }
    }

    @Override
    public boolean isEnabled()
    {
        try {
            WatermarkConfig config = getConfiguration();
            return config.isEnabled();
        } catch (Exception e) {
            LOGGER.error("Failed to check if watermark is enabled", e);
            return false; // 出错时默认禁用
        }
    }

    @Override
    public boolean isMobileEnvironment()
    {
        try {
            ExecutionContext context = execution.getContext();
            if (context != null) {
                // 这里可以添加更复杂的移动端检测逻辑
                // 目前返回false，后续可以通过HTTP头部或其他方式检测
                return false;
            }
        } catch (Exception e) {
            LOGGER.debug("Failed to detect mobile environment", e);
        }
        return false;
    }

    @Override
    public String getCurrentUserDisplayName()
    {
        try {
            DocumentReference userReference = documentAccessBridge.getCurrentUserReference();
            if (userReference != null) {
                String userName = entityReferenceSerializer.serialize(userReference);
                // 提取用户名部分（去掉XWiki.前缀）
                if (userName.startsWith("XWiki.")) {
                    return userName.substring(6);
                }
                return userName;
            }
        } catch (Exception e) {
            LOGGER.debug("Failed to get current user display name", e);
        }
        return "Guest";
    }

    @Override
    public void refreshConfiguration()
    {
        cacheLock.writeLock().lock();
        try {
            cachedConfig = null;
            cacheTimestamp = 0;
            LOGGER.info("Configuration cache cleared");
        } finally {
            cacheLock.writeLock().unlock();
        }
    }
}
