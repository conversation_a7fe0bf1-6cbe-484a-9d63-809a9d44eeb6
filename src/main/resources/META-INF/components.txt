# XWiki Watermark Extension Components
# This file registers all components that should be loaded by the XWiki container.
# Each line contains the fully qualified class name of a component.

# Default watermark service implementation
com.microcredchina.xwikiwatermark.internal.DefaultWatermarkService

# Watermark script service for frontend access
com.microcredchina.xwikiwatermark.internal.WatermarkScriptService
