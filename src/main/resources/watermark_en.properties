# XWiki Watermark Extension - English Localization
# 
# This file contains English translations for the watermark extension
# administration interface and user messages.
#
# @version 1.0
# @since XWiki 17.4.3

# Administration page
watermark.admin.title=Watermark Configuration
watermark.admin.description=Configure watermark settings for your XWiki instance. Watermarks help protect your content and provide visual identification.

# Status section
watermark.admin.status.title=Service Status
watermark.admin.status.service=Service
watermark.admin.status.available=Available
watermark.admin.status.unavailable=Unavailable
watermark.admin.status.enabled=Status
watermark.admin.status.on=Enabled
watermark.admin.status.off=Disabled
watermark.admin.status.currentUser=Current User
watermark.admin.status.mobile=Mobile Device
watermark.admin.status.yes=Yes
watermark.admin.status.no=No

# Basic settings
watermark.admin.basic.title=Basic Settings
watermark.admin.basic.enabled=Enable Watermark
watermark.admin.basic.enabled.help=Enable or disable the watermark functionality globally.
watermark.admin.basic.textTemplate=Text Template
watermark.admin.basic.textTemplate.placeholder=Enter watermark text template
watermark.admin.basic.textTemplate.help=Define the watermark text. Use placeholders for dynamic content.
watermark.admin.basic.placeholders=Available placeholders
watermark.admin.basic.antiCopy=Enable Anti-Copy Protection
watermark.admin.basic.antiCopy.help=Prevent users from selecting and copying page content.
watermark.admin.basic.applyToMobile=Apply to Mobile Devices
watermark.admin.basic.applyToMobile.help=Show watermarks on mobile devices and tablets.

# Style settings
watermark.admin.style.title=Style Settings
watermark.admin.style.fontSize=Font Size
watermark.admin.style.fontSize.help=Size of the watermark text in pixels (8-72px).
watermark.admin.style.opacity=Opacity
watermark.admin.style.opacity.help=Transparency level of the watermark (0.01-1.0): 
watermark.admin.style.angle=Rotation Angle
watermark.admin.style.angle.help=Rotation angle of the watermark text in degrees (-90° to 90°).
watermark.admin.style.xSpacing=Horizontal Spacing
watermark.admin.style.ySpacing=Vertical Spacing
watermark.admin.style.spacing.help=Distance between watermark repetitions in pixels.

# Preview section
watermark.admin.preview.title=Preview
watermark.admin.preview.placeholder=Click "Update Preview" to see watermark preview
watermark.admin.preview.update=Update Preview
watermark.admin.preview.help=Preview shows approximate appearance. Actual watermark may vary.

# Actions
watermark.admin.actions.save=Save Configuration
watermark.admin.actions.reset=Reset to Defaults
watermark.admin.actions.refresh=Refresh Watermark
watermark.admin.actions.saving=Saving...
watermark.admin.actions.reset.confirm=Are you sure you want to reset all settings to defaults?
watermark.admin.actions.refresh.success=Watermark refreshed successfully!
watermark.admin.actions.refresh.error=Failed to refresh watermark. Please check if the watermark service is available.

# Validation messages
watermark.admin.validation.textTemplate.required=Text template is required. Please enter a watermark text.

# General messages
watermark.message.configSaved=Watermark configuration saved successfully.
watermark.message.configError=Failed to save watermark configuration. Please try again.
watermark.message.serviceUnavailable=Watermark service is currently unavailable.
