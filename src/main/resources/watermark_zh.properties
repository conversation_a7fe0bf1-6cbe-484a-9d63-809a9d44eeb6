# XWiki Watermark Extension - Chinese Localization
# 
# This file contains Chinese translations for the watermark extension
# administration interface and user messages.
#
# @version 1.0
# @since XWiki 17.4.3

# Administration page
watermark.admin.title=????
watermark.admin.description=???XWiki????????????????????????????

# Status section
watermark.admin.status.title=????
watermark.admin.status.service=??
watermark.admin.status.available=??
watermark.admin.status.unavailable=???
watermark.admin.status.enabled=??
watermark.admin.status.on=???
watermark.admin.status.off=???
watermark.admin.status.currentUser=????
watermark.admin.status.mobile=????
watermark.admin.status.yes=?
watermark.admin.status.no=?

# Basic settings
watermark.admin.basic.title=????
watermark.admin.basic.enabled=????
watermark.admin.basic.enabled.help=????????????
watermark.admin.basic.textTemplate=????
watermark.admin.basic.textTemplate.placeholder=?????????
watermark.admin.basic.textTemplate.help=???????????????????
watermark.admin.basic.placeholders=?????
watermark.admin.basic.antiCopy=???????
watermark.admin.basic.antiCopy.help=??????????????
watermark.admin.basic.applyToMobile=???????
watermark.admin.basic.applyToMobile.help=????????????????

# Style settings
watermark.admin.style.title=????
watermark.admin.style.fontSize=????
watermark.admin.style.fontSize.help=??????????????8-72px??
watermark.admin.style.opacity=???
watermark.admin.style.opacity.help=?????????0.01-1.0??
watermark.admin.style.angle=????
watermark.admin.style.angle.help=???????????????-90°?90°??
watermark.admin.style.xSpacing=????
watermark.admin.style.ySpacing=????
watermark.admin.style.spacing.help=????????????????

# Preview section
watermark.admin.preview.title=??
watermark.admin.preview.placeholder=??"????"????????
watermark.admin.preview.update=????
watermark.admin.preview.help=????????????????????

# Actions
watermark.admin.actions.save=????
watermark.admin.actions.reset=??????
watermark.admin.actions.refresh=????
watermark.admin.actions.saving=???...
watermark.admin.actions.reset.confirm=?????????????????
watermark.admin.actions.refresh.success=???????
watermark.admin.actions.refresh.error=???????????????????

# Validation messages
watermark.admin.validation.textTemplate.required=?????????????????

# General messages
watermark.message.configSaved=?????????
watermark.message.configError=?????????????
watermark.message.serviceUnavailable=??????????
