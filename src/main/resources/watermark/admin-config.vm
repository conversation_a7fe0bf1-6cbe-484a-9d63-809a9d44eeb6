##
## XWiki Watermark Extension - Administration Configuration Page
##
## This Velocity template provides the administration interface for configuring
## the watermark extension. It integrates with XWiki's standard admin panel
## and supports internationalization.
##
## @version 1.0
## @since XWiki 17.4.3
##

#set($watermarkService = $services.watermark)
#set($currentConfig = $watermarkService.getConfig())

## Page title and description
<div class="admin-page-content">
  <div id="HWatermarkConfiguration" class="admin-page-title">
    <h1>$services.localization.render('watermark.admin.title')</h1>
    <p class="admin-page-description">$services.localization.render('watermark.admin.description')</p>
  </div>

  ## Status information
  <div class="panel panel-default">
    <div class="panel-heading">
      <h3 class="panel-title">$services.localization.render('watermark.admin.status.title')</h3>
    </div>
    <div class="panel-body">
      <div class="row">
        <div class="col-md-6">
          <strong>$services.localization.render('watermark.admin.status.service'):</strong>
          #if($watermarkService)
            <span class="label label-success">$services.localization.render('watermark.admin.status.available')</span>
          #else
            <span class="label label-danger">$services.localization.render('watermark.admin.status.unavailable')</span>
          #end
        </div>
        <div class="col-md-6">
          <strong>$services.localization.render('watermark.admin.status.enabled'):</strong>
          #if($currentConfig.enabled)
            <span class="label label-success">$services.localization.render('watermark.admin.status.on')</span>
          #else
            <span class="label label-default">$services.localization.render('watermark.admin.status.off')</span>
          #end
        </div>
      </div>
      <div class="row" style="margin-top: 10px;">
        <div class="col-md-6">
          <strong>$services.localization.render('watermark.admin.status.currentUser'):</strong>
          <span class="text-info">$watermarkService.getCurrentUser()</span>
        </div>
        <div class="col-md-6">
          <strong>$services.localization.render('watermark.admin.status.mobile'):</strong>
          #if($watermarkService.isMobileEnvironment())
            <span class="label label-info">$services.localization.render('watermark.admin.status.yes')</span>
          #else
            <span class="label label-default">$services.localization.render('watermark.admin.status.no')</span>
          #end
        </div>
      </div>
    </div>
  </div>

  ## Configuration form
  <form id="watermarkConfigForm" class="xform" method="post" action="$doc.getURL('save')">
    <input type="hidden" name="form_token" value="$!{services.csrf.getToken()}" />
    <input type="hidden" name="xredirect" value="$doc.getURL('admin', 'editor=globaladmin&amp;section=Watermark')" />

    ## Basic Settings Panel
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">$services.localization.render('watermark.admin.basic.title')</h3>
      </div>
      <div class="panel-body">
        ## Enable/Disable watermark
        <div class="form-group">
          <label class="control-label">
            <input type="checkbox" name="watermark.enabled" value="1" 
                   #if($currentConfig.enabled)checked="checked"#end />
            $services.localization.render('watermark.admin.basic.enabled')
          </label>
          <div class="help-block">$services.localization.render('watermark.admin.basic.enabled.help')</div>
        </div>

        ## Text template
        <div class="form-group">
          <label for="watermark_textTemplate" class="control-label">
            $services.localization.render('watermark.admin.basic.textTemplate')
          </label>
          <input type="text" id="watermark_textTemplate" name="watermark.textTemplate" 
                 class="form-control" value="$!{currentConfig.textTemplate}" 
                 placeholder="$services.localization.render('watermark.admin.basic.textTemplate.placeholder')" />
          <div class="help-block">
            $services.localization.render('watermark.admin.basic.textTemplate.help')
            <br/>
            <strong>$services.localization.render('watermark.admin.basic.placeholders'):</strong>
            <code>${user}</code>, <code>${timestamp}</code>, <code>${date}</code>, <code>${time}</code>
          </div>
        </div>

        ## Anti-copy protection
        <div class="form-group">
          <label class="control-label">
            <input type="checkbox" name="watermark.antiCopy" value="1" 
                   #if($currentConfig.antiCopy)checked="checked"#end />
            $services.localization.render('watermark.admin.basic.antiCopy')
          </label>
          <div class="help-block">$services.localization.render('watermark.admin.basic.antiCopy.help')</div>
        </div>

        ## Mobile support
        <div class="form-group">
          <label class="control-label">
            <input type="checkbox" name="watermark.applyToMobile" value="1" 
                   #if($currentConfig.applyToMobile)checked="checked"#end />
            $services.localization.render('watermark.admin.basic.applyToMobile')
          </label>
          <div class="help-block">$services.localization.render('watermark.admin.basic.applyToMobile.help')</div>
        </div>
      </div>
    </div>

    ## Style Settings Panel
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">$services.localization.render('watermark.admin.style.title')</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          ## Font size
          <div class="col-md-6">
            <div class="form-group">
              <label for="watermark_fontSize" class="control-label">
                $services.localization.render('watermark.admin.style.fontSize')
              </label>
              <div class="input-group">
                <input type="number" id="watermark_fontSize" name="watermark.fontSize" 
                       class="form-control" value="$!{currentConfig.fontSize}" 
                       min="8" max="72" step="1" />
                <span class="input-group-addon">px</span>
              </div>
              <div class="help-block">$services.localization.render('watermark.admin.style.fontSize.help')</div>
            </div>
          </div>

          ## Opacity
          <div class="col-md-6">
            <div class="form-group">
              <label for="watermark_opacity" class="control-label">
                $services.localization.render('watermark.admin.style.opacity')
              </label>
              <input type="range" id="watermark_opacity" name="watermark.opacity" 
                     class="form-control" value="$!{currentConfig.opacity}" 
                     min="0.01" max="1.0" step="0.01" 
                     oninput="document.getElementById('opacityValue').textContent = this.value" />
              <div class="help-block">
                $services.localization.render('watermark.admin.style.opacity.help')
                <span id="opacityValue">$!{currentConfig.opacity}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="row">
          ## Rotation angle
          <div class="col-md-6">
            <div class="form-group">
              <label for="watermark_angle" class="control-label">
                $services.localization.render('watermark.admin.style.angle')
              </label>
              <div class="input-group">
                <input type="number" id="watermark_angle" name="watermark.angle" 
                       class="form-control" value="$!{currentConfig.angle}" 
                       min="-90" max="90" step="1" />
                <span class="input-group-addon">°</span>
              </div>
              <div class="help-block">$services.localization.render('watermark.admin.style.angle.help')</div>
            </div>
          </div>

          ## Spacing controls in a sub-row
          <div class="col-md-6">
            <div class="row">
              <div class="col-sm-6">
                <div class="form-group">
                  <label for="watermark_xSpacing" class="control-label">
                    $services.localization.render('watermark.admin.style.xSpacing')
                  </label>
                  <div class="input-group">
                    <input type="number" id="watermark_xSpacing" name="watermark.xSpacing" 
                           class="form-control" value="$!{currentConfig.xSpacing}" 
                           min="50" max="500" step="10" />
                    <span class="input-group-addon">px</span>
                  </div>
                </div>
              </div>
              <div class="col-sm-6">
                <div class="form-group">
                  <label for="watermark_ySpacing" class="control-label">
                    $services.localization.render('watermark.admin.style.ySpacing')
                  </label>
                  <div class="input-group">
                    <input type="number" id="watermark_ySpacing" name="watermark.ySpacing" 
                           class="form-control" value="$!{currentConfig.ySpacing}" 
                           min="50" max="500" step="10" />
                    <span class="input-group-addon">px</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="help-block">$services.localization.render('watermark.admin.style.spacing.help')</div>
          </div>
        </div>
      </div>
    </div>

    ## Preview Panel
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">$services.localization.render('watermark.admin.preview.title')</h3>
      </div>
      <div class="panel-body">
        <div id="watermarkPreview" style="position: relative; height: 200px; border: 1px solid #ddd; background: #f9f9f9; overflow: hidden;">
          <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: #999;">
            $services.localization.render('watermark.admin.preview.placeholder')
          </div>
        </div>
        <div style="margin-top: 10px;">
          <button type="button" id="previewButton" class="btn btn-info">
            $services.localization.render('watermark.admin.preview.update')
          </button>
          <span class="help-block" style="display: inline-block; margin-left: 10px;">
            $services.localization.render('watermark.admin.preview.help')
          </span>
        </div>
      </div>
    </div>

    ## Action buttons
    <div class="form-actions">
      <button type="submit" class="btn btn-primary">
        $services.localization.render('watermark.admin.actions.save')
      </button>
      <button type="button" id="resetButton" class="btn btn-default">
        $services.localization.render('watermark.admin.actions.reset')
      </button>
      <button type="button" id="refreshButton" class="btn btn-info">
        $services.localization.render('watermark.admin.actions.refresh')
      </button>
    </div>
  </form>
</div>

## JavaScript for form interactions
<script type="text/javascript">
//<![CDATA[
document.addEventListener('DOMContentLoaded', function() {
  // Preview functionality
  document.getElementById('previewButton').addEventListener('click', function() {
    var preview = document.getElementById('watermarkPreview');
    var textTemplate = document.getElementById('watermark_textTemplate').value || '${user} - ${timestamp}';
    var fontSize = document.getElementById('watermark_fontSize').value || 16;
    var opacity = document.getElementById('watermark_opacity').value || 0.1;
    var angle = document.getElementById('watermark_angle').value || -15;
    
    // Simple preview simulation
    preview.innerHTML = '<div style="position: absolute; top: 20px; left: 20px; font-size: ' + fontSize + 'px; opacity: ' + opacity + '; transform: rotate(' + angle + 'deg); color: #333; font-family: Arial, sans-serif;">' + textTemplate.replace('${user}', '$watermarkService.getCurrentUser()').replace('${timestamp}', new Date().toLocaleString()) + '</div>';
  });
  
  // Reset functionality
  document.getElementById('resetButton').addEventListener('click', function() {
    if (confirm('$services.localization.render("watermark.admin.actions.reset.confirm")')) {
      location.reload();
    }
  });
  
  // Refresh functionality
  document.getElementById('refreshButton').addEventListener('click', function() {
    if (window.XWikiWatermark) {
      window.XWikiWatermark.refresh();
      alert('$services.localization.render("watermark.admin.actions.refresh.success")');
    } else {
      alert('$services.localization.render("watermark.admin.actions.refresh.error")');
    }
  });
  
  // Form validation
  document.getElementById('watermarkConfigForm').addEventListener('submit', function(e) {
    var textTemplate = document.getElementById('watermark_textTemplate').value.trim();
    if (!textTemplate) {
      alert('$services.localization.render("watermark.admin.validation.textTemplate.required")');
      e.preventDefault();
      return false;
    }
    
    // Show saving indicator
    var submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.textContent = '$services.localization.render("watermark.admin.actions.saving")';
  });
});
//]]>
</script>
