/**
 * XWiki Watermark Extension - Styles and Anti-Copy Protection
 * 
 * This stylesheet provides:
 * - Watermark container and canvas styling
 * - Anti-copy protection features
 * - Mobile-responsive design
 * - Performance optimizations
 * 
 * @version 1.0
 * @since XWiki 17.4.3
 */

/* ==========================================================================
   Watermark Container Styles
   ========================================================================== */

/**
 * Main watermark container
 * - Fixed positioning to cover entire viewport
 * - High z-index to ensure visibility
 * - Pointer events disabled to avoid interfering with page interaction
 */
#xwiki-watermark-container {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    pointer-events: none !important;
    z-index: 9999 !important;
    overflow: hidden !important;
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    /* Prevent watermark from being printed */
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
}

/**
 * Watermark canvas element
 * - Full size to match container
 * - Optimized for performance
 */
#xwiki-watermark-canvas {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    display: block !important;
    /* Performance optimizations */
    will-change: transform !important;
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
    /* Prevent canvas selection */
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    /* Disable context menu */
    -webkit-touch-callout: none !important;
    -webkit-tap-highlight-color: transparent !important;
}

/* ==========================================================================
   Anti-Copy Protection Styles
   ========================================================================== */

/**
 * Anti-copy protection for body when enabled
 * Applied dynamically via JavaScript when antiCopy is enabled
 */
body.xwiki-watermark-anticopy {
    /* Disable text selection */
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    
    /* Disable drag and drop */
    -webkit-user-drag: none !important;
    -khtml-user-drag: none !important;
    -moz-user-drag: none !important;
    -o-user-drag: none !important;
    user-drag: none !important;
    
    /* Disable context menu */
    -webkit-touch-callout: none !important;
    -webkit-tap-highlight-color: transparent !important;
}

/**
 * Disable selection for all elements when anti-copy is enabled
 */
body.xwiki-watermark-anticopy * {
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    
    /* Disable drag for images and other elements */
    -webkit-user-drag: none !important;
    -khtml-user-drag: none !important;
    -moz-user-drag: none !important;
    -o-user-drag: none !important;
    user-drag: none !important;
}

/**
 * Prevent image saving and dragging
 */
body.xwiki-watermark-anticopy img {
    pointer-events: none !important;
    -webkit-user-drag: none !important;
    -khtml-user-drag: none !important;
    -moz-user-drag: none !important;
    -o-user-drag: none !important;
    user-drag: none !important;
}

/* ==========================================================================
   Mobile and Responsive Styles
   ========================================================================== */

/**
 * Mobile-specific optimizations
 */
@media screen and (max-width: 768px) {
    #xwiki-watermark-container {
        /* Ensure proper rendering on mobile */
        -webkit-overflow-scrolling: touch !important;
        /* Optimize for mobile performance */
        -webkit-backface-visibility: hidden !important;
        backface-visibility: hidden !important;
    }
    
    #xwiki-watermark-canvas {
        /* Mobile canvas optimizations */
        -webkit-backface-visibility: hidden !important;
        backface-visibility: hidden !important;
        /* Improve touch performance */
        touch-action: none !important;
    }
}

/**
 * Tablet-specific adjustments
 */
@media screen and (min-width: 769px) and (max-width: 1024px) {
    #xwiki-watermark-container {
        /* Tablet-specific optimizations if needed */
    }
}

/**
 * High DPI display support
 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    #xwiki-watermark-canvas {
        /* Ensure crisp rendering on high DPI displays */
        image-rendering: -webkit-optimize-contrast !important;
        image-rendering: crisp-edges !important;
    }
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

/**
 * Hide watermark when printing (optional)
 * Can be overridden if watermark should appear in print
 */
@media print {
    #xwiki-watermark-container {
        display: none !important;
    }
}

/* ==========================================================================
   Accessibility and Performance
   ========================================================================== */

/**
 * Reduce motion for users who prefer it
 */
@media (prefers-reduced-motion: reduce) {
    #xwiki-watermark-container,
    #xwiki-watermark-canvas {
        animation: none !important;
        transition: none !important;
    }
}

/**
 * Dark mode compatibility
 */
@media (prefers-color-scheme: dark) {
    /* Watermark colors are handled by JavaScript based on configuration */
    /* This section can be extended for dark mode specific adjustments */
}

/* ==========================================================================
   Browser-Specific Fixes
   ========================================================================== */

/**
 * Internet Explorer compatibility
 */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    #xwiki-watermark-container {
        /* IE-specific fixes if needed */
    }
}

/**
 * Safari-specific optimizations
 */
@supports (-webkit-appearance: none) {
    #xwiki-watermark-canvas {
        /* Safari-specific canvas optimizations */
        -webkit-transform: translate3d(0, 0, 0) !important;
    }
}

/* ==========================================================================
   Animation and Transition Classes
   ========================================================================== */

/**
 * Fade-in animation for watermark appearance
 */
.xwiki-watermark-fade-in {
    opacity: 0 !important;
    animation: xwiki-watermark-fadein 0.5s ease-in-out forwards !important;
}

@keyframes xwiki-watermark-fadein {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/**
 * Fade-out animation for watermark removal
 */
.xwiki-watermark-fade-out {
    animation: xwiki-watermark-fadeout 0.3s ease-in-out forwards !important;
}

@keyframes xwiki-watermark-fadeout {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

/**
 * Hide watermark temporarily (for debugging or special cases)
 */
.xwiki-watermark-hidden {
    display: none !important;
}

/**
 * Disable watermark interactions completely
 */
.xwiki-watermark-disabled {
    pointer-events: none !important;
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
}
