/**
 * XWiki Watermark Extension - Canvas Watermark Renderer
 * 
 * This module provides high-quality watermark rendering using Canvas API.
 * Features:
 * - Dynamic text watermarks with configurable parameters
 * - Performance optimization with debouncing and RAF
 * - Mobile-responsive design
 * - Error handling and fallback mechanisms
 * 
 * @version 1.0
 * @since XWiki 17.4.3
 */

(function(window, document) {
    'use strict';

    // Namespace for watermark functionality
    var XWikiWatermark = {
        // Configuration cache
        config: null,
        
        // Canvas and context references
        canvas: null,
        context: null,
        
        // Performance optimization
        debounceTimer: null,
        rafId: null,
        
        // State management
        isInitialized: false,
        isRendering: false,
        
        // Constants
        DEBOUNCE_DELAY: 250,
        CANVAS_ID: 'xwiki-watermark-canvas',
        CONTAINER_ID: 'xwiki-watermark-container'
    };

    /**
     * Initialize the watermark system
     */
    XWikiWatermark.init = function() {
        if (this.isInitialized) {
            return;
        }

        try {
            // Load configuration from backend
            this.loadConfiguration(function(success) {
                if (success) {
                    XWikiWatermark.setupWatermark();
                    XWikiWatermark.bindEvents();
                    XWikiWatermark.isInitialized = true;
                    console.log('[XWiki Watermark] Initialized successfully');
                } else {
                    console.warn('[XWiki Watermark] Failed to load configuration');
                }
            });
        } catch (error) {
            console.error('[XWiki Watermark] Initialization failed:', error);
        }
    };

    /**
     * Load configuration from XWiki backend
     */
    XWikiWatermark.loadConfiguration = function(callback) {
        try {
            // Check if XWiki services are available
            if (typeof window.$services === 'undefined' || !window.$services.watermark) {
                console.warn('[XWiki Watermark] XWiki services not available');
                callback(false);
                return;
            }

            // Get configuration from script service
            var config = window.$services.watermark.getConfig();
            
            if (config && typeof config === 'object') {
                this.config = {
                    enabled: config.enabled || false,
                    textTemplate: config.textTemplate || '',
                    xSpacing: parseInt(config.xSpacing) || 200,
                    ySpacing: parseInt(config.ySpacing) || 150,
                    angle: parseInt(config.angle) || -15,
                    opacity: parseFloat(config.opacity) || 0.1,
                    fontSize: parseInt(config.fontSize) || 16,
                    antiCopy: config.antiCopy || false,
                    applyToMobile: config.applyToMobile !== false
                };
                
                console.log('[XWiki Watermark] Configuration loaded:', this.config);
                callback(true);
            } else {
                console.warn('[XWiki Watermark] Invalid configuration received');
                callback(false);
            }
        } catch (error) {
            console.error('[XWiki Watermark] Failed to load configuration:', error);
            callback(false);
        }
    };

    /**
     * Setup watermark rendering
     */
    XWikiWatermark.setupWatermark = function() {
        if (!this.config || !this.config.enabled) {
            return;
        }

        // Check mobile environment
        if (!this.config.applyToMobile && this.isMobileDevice()) {
            console.log('[XWiki Watermark] Skipping on mobile device');
            return;
        }

        // Create watermark container and canvas
        this.createWatermarkElements();
        
        // Initial render
        this.renderWatermark();
    };

    /**
     * Create watermark DOM elements
     */
    XWikiWatermark.createWatermarkElements = function() {
        // Remove existing watermark if any
        this.removeWatermark();

        // Create container
        var container = document.createElement('div');
        container.id = this.CONTAINER_ID;
        container.style.cssText = [
            'position: fixed',
            'top: 0',
            'left: 0',
            'width: 100%',
            'height: 100%',
            'pointer-events: none',
            'z-index: 9999',
            'overflow: hidden'
        ].join(';');

        // Create canvas
        this.canvas = document.createElement('canvas');
        this.canvas.id = this.CANVAS_ID;
        this.canvas.style.cssText = [
            'position: absolute',
            'top: 0',
            'left: 0',
            'width: 100%',
            'height: 100%'
        ].join(';');

        // Get canvas context
        this.context = this.canvas.getContext('2d');
        if (!this.context) {
            console.error('[XWiki Watermark] Canvas not supported');
            return;
        }

        // Append to DOM
        container.appendChild(this.canvas);
        document.body.appendChild(container);
    };

    /**
     * Render watermark on canvas
     */
    XWikiWatermark.renderWatermark = function() {
        if (this.isRendering || !this.canvas || !this.context || !this.config) {
            return;
        }

        this.isRendering = true;

        try {
            // Set canvas size to match viewport
            var rect = this.getViewportSize();
            this.canvas.width = rect.width;
            this.canvas.height = rect.height;

            // Clear canvas
            this.context.clearRect(0, 0, rect.width, rect.height);

            // Get processed text
            var text = this.getProcessedText();
            if (!text) {
                this.isRendering = false;
                return;
            }

            // Configure canvas context
            this.configureCanvasContext(text);

            // Calculate text dimensions
            var textMetrics = this.context.measureText(text);
            var textWidth = textMetrics.width;
            var textHeight = this.config.fontSize;

            // Render watermark pattern
            this.renderWatermarkPattern(text, textWidth, textHeight, rect);

        } catch (error) {
            console.error('[XWiki Watermark] Render failed:', error);
        } finally {
            this.isRendering = false;
        }
    };

    /**
     * Configure canvas rendering context
     */
    XWikiWatermark.configureCanvasContext = function(text) {
        var ctx = this.context;
        
        // Set font
        ctx.font = this.config.fontSize + 'px Arial, sans-serif';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        
        // Set color and opacity
        ctx.fillStyle = 'rgba(0, 0, 0, ' + this.config.opacity + ')';
        
        // Enable text smoothing
        ctx.imageSmoothingEnabled = true;
        if (ctx.textRenderingOptimization) {
            ctx.textRenderingOptimization = 'optimizeQuality';
        }
    };

    /**
     * Render watermark pattern across the canvas
     */
    XWikiWatermark.renderWatermarkPattern = function(text, textWidth, textHeight, viewport) {
        var ctx = this.context;
        var angle = this.config.angle * Math.PI / 180; // Convert to radians
        
        // Calculate pattern bounds
        var cols = Math.ceil(viewport.width / this.config.xSpacing) + 2;
        var rows = Math.ceil(viewport.height / this.config.ySpacing) + 2;
        
        // Render watermark grid
        for (var row = 0; row < rows; row++) {
            for (var col = 0; col < cols; col++) {
                var x = col * this.config.xSpacing;
                var y = row * this.config.ySpacing;
                
                // Add offset for alternating rows
                if (row % 2 === 1) {
                    x += this.config.xSpacing / 2;
                }
                
                // Save context state
                ctx.save();
                
                // Translate and rotate
                ctx.translate(x, y);
                ctx.rotate(angle);
                
                // Draw text
                ctx.fillText(text, 0, 0);
                
                // Restore context state
                ctx.restore();
            }
        }
    };

    /**
     * Get processed watermark text
     */
    XWikiWatermark.getProcessedText = function() {
        try {
            if (window.$services && window.$services.watermark) {
                return window.$services.watermark.processText(this.config.textTemplate);
            }
        } catch (error) {
            console.error('[XWiki Watermark] Failed to process text:', error);
        }
        
        // Fallback to template
        return this.config.textTemplate || '';
    };

    /**
     * Get viewport dimensions
     */
    XWikiWatermark.getViewportSize = function() {
        return {
            width: Math.max(document.documentElement.clientWidth, window.innerWidth || 0),
            height: Math.max(document.documentElement.clientHeight, window.innerHeight || 0)
        };
    };

    /**
     * Check if current device is mobile
     */
    XWikiWatermark.isMobileDevice = function() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               (window.innerWidth <= 768);
    };

    /**
     * Bind event listeners
     */
    XWikiWatermark.bindEvents = function() {
        var self = this;
        
        // Debounced resize handler
        var resizeHandler = function() {
            if (self.debounceTimer) {
                clearTimeout(self.debounceTimer);
            }
            
            self.debounceTimer = setTimeout(function() {
                if (self.rafId) {
                    cancelAnimationFrame(self.rafId);
                }
                
                self.rafId = requestAnimationFrame(function() {
                    self.renderWatermark();
                });
            }, self.DEBOUNCE_DELAY);
        };
        
        // Bind resize events
        window.addEventListener('resize', resizeHandler, { passive: true });
        window.addEventListener('orientationchange', resizeHandler, { passive: true });
        
        // Bind visibility change
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden && self.config && self.config.enabled) {
                self.renderWatermark();
            }
        });
    };

    /**
     * Remove watermark from DOM
     */
    XWikiWatermark.removeWatermark = function() {
        var existing = document.getElementById(this.CONTAINER_ID);
        if (existing) {
            existing.parentNode.removeChild(existing);
        }
        
        this.canvas = null;
        this.context = null;
    };

    /**
     * Refresh watermark (public API)
     */
    XWikiWatermark.refresh = function() {
        var self = this;
        this.loadConfiguration(function(success) {
            if (success) {
                self.setupWatermark();
            }
        });
    };

    /**
     * Destroy watermark (public API)
     */
    XWikiWatermark.destroy = function() {
        // Clear timers
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }
        if (this.rafId) {
            cancelAnimationFrame(this.rafId);
        }
        
        // Remove DOM elements
        this.removeWatermark();
        
        // Reset state
        this.isInitialized = false;
        this.isRendering = false;
        this.config = null;
    };

    // Expose to global scope
    window.XWikiWatermark = XWikiWatermark;

    // Auto-initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            XWikiWatermark.init();
        });
    } else {
        // DOM already loaded
        setTimeout(function() {
            XWikiWatermark.init();
        }, 100);
    }

})(window, document);
