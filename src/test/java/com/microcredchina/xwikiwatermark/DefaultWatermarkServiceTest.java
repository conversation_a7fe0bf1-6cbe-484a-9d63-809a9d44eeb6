/*
 * See the NOTICE file distributed with this work for additional
 * information regarding copyright ownership.
 *
 * This is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as
 * published by the Free Software Foundation; either version 2.1 of
 * the License, or (at your option) any later version.
 *
 * This software is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this software; if not, write to the Free
 * Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA
 * 02110-1301 USA, or see the FSF site: http://www.fsf.org.
 */
package com.microcredchina.xwikiwatermark;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.xwiki.bridge.DocumentAccessBridge;
import org.xwiki.configuration.ConfigurationSource;
import org.xwiki.context.Execution;
import org.xwiki.context.ExecutionContext;
import org.xwiki.model.reference.DocumentReference;
import org.xwiki.model.reference.EntityReferenceSerializer;

import com.microcredchina.xwikiwatermark.internal.DefaultWatermarkService;

/**
 * Unit tests for DefaultWatermarkService.
 * 
 * @version $Id$
 * @since 1.0
 */
@ExtendWith(MockitoExtension.class)
class DefaultWatermarkServiceTest
{
    @Mock
    private ConfigurationSource configurationSource;

    @Mock
    private DocumentAccessBridge documentAccessBridge;

    @Mock
    private Execution execution;

    @Mock
    private EntityReferenceSerializer<String> entityReferenceSerializer;

    @Mock
    private ExecutionContext executionContext;

    @Mock
    private DocumentReference userReference;

    private DefaultWatermarkService watermarkService;

    @BeforeEach
    void setUp()
    {
        watermarkService = new DefaultWatermarkService();
        
        // Use reflection to inject mocked dependencies
        try {
            java.lang.reflect.Field configField = DefaultWatermarkService.class.getDeclaredField("configurationSource");
            configField.setAccessible(true);
            configField.set(watermarkService, configurationSource);

            java.lang.reflect.Field bridgeField = DefaultWatermarkService.class.getDeclaredField("documentAccessBridge");
            bridgeField.setAccessible(true);
            bridgeField.set(watermarkService, documentAccessBridge);

            java.lang.reflect.Field executionField = DefaultWatermarkService.class.getDeclaredField("execution");
            executionField.setAccessible(true);
            executionField.set(watermarkService, execution);

            java.lang.reflect.Field serializerField = DefaultWatermarkService.class.getDeclaredField("entityReferenceSerializer");
            serializerField.setAccessible(true);
            serializerField.set(watermarkService, entityReferenceSerializer);
        } catch (Exception e) {
            fail("Failed to inject dependencies: " + e.getMessage());
        }
    }

    @Test
    void testGetConfiguration_DefaultValues()
    {
        // Setup: Mock configuration source to return default values
        when(configurationSource.getProperty("watermark.enabled", false)).thenReturn(false);
        when(configurationSource.getProperty("watermark.textTemplate", WatermarkConfig.DEFAULT_TEXT_TEMPLATE))
            .thenReturn(WatermarkConfig.DEFAULT_TEXT_TEMPLATE);
        when(configurationSource.getProperty("watermark.xSpacing", WatermarkConfig.DEFAULT_X_SPACING))
            .thenReturn(WatermarkConfig.DEFAULT_X_SPACING);
        when(configurationSource.getProperty("watermark.ySpacing", WatermarkConfig.DEFAULT_Y_SPACING))
            .thenReturn(WatermarkConfig.DEFAULT_Y_SPACING);
        when(configurationSource.getProperty("watermark.angle", WatermarkConfig.DEFAULT_ANGLE))
            .thenReturn(WatermarkConfig.DEFAULT_ANGLE);
        when(configurationSource.getProperty("watermark.opacity", WatermarkConfig.DEFAULT_OPACITY))
            .thenReturn(WatermarkConfig.DEFAULT_OPACITY);
        when(configurationSource.getProperty("watermark.fontSize", WatermarkConfig.DEFAULT_FONT_SIZE))
            .thenReturn(WatermarkConfig.DEFAULT_FONT_SIZE);
        when(configurationSource.getProperty("watermark.antiCopy", false)).thenReturn(false);
        when(configurationSource.getProperty("watermark.applyToMobile", true)).thenReturn(true);

        // Execute
        WatermarkConfig config = watermarkService.getConfiguration();

        // Verify
        assertNotNull(config);
        assertFalse(config.isEnabled());
        assertEquals(WatermarkConfig.DEFAULT_TEXT_TEMPLATE, config.getTextTemplate());
        assertEquals(WatermarkConfig.DEFAULT_X_SPACING, config.getXSpacing());
        assertEquals(WatermarkConfig.DEFAULT_Y_SPACING, config.getYSpacing());
        assertEquals(WatermarkConfig.DEFAULT_ANGLE, config.getAngle());
        assertEquals(WatermarkConfig.DEFAULT_OPACITY, config.getOpacity());
        assertEquals(WatermarkConfig.DEFAULT_FONT_SIZE, config.getFontSize());
        assertFalse(config.isAntiCopy());
        assertTrue(config.isApplyToMobile());
    }

    @Test
    void testGetConfiguration_CustomValues()
    {
        // Setup: Mock configuration source to return custom values
        when(configurationSource.getProperty("watermark.enabled", false)).thenReturn(true);
        when(configurationSource.getProperty("watermark.textTemplate", WatermarkConfig.DEFAULT_TEXT_TEMPLATE))
            .thenReturn("Custom ${user} Template");
        when(configurationSource.getProperty("watermark.xSpacing", WatermarkConfig.DEFAULT_X_SPACING))
            .thenReturn(300);
        when(configurationSource.getProperty("watermark.ySpacing", WatermarkConfig.DEFAULT_Y_SPACING))
            .thenReturn(250);
        when(configurationSource.getProperty("watermark.angle", WatermarkConfig.DEFAULT_ANGLE))
            .thenReturn(45);
        when(configurationSource.getProperty("watermark.opacity", WatermarkConfig.DEFAULT_OPACITY))
            .thenReturn(0.5);
        when(configurationSource.getProperty("watermark.fontSize", WatermarkConfig.DEFAULT_FONT_SIZE))
            .thenReturn(24);
        when(configurationSource.getProperty("watermark.antiCopy", false)).thenReturn(true);
        when(configurationSource.getProperty("watermark.applyToMobile", true)).thenReturn(false);

        // Execute
        WatermarkConfig config = watermarkService.getConfiguration();

        // Verify
        assertNotNull(config);
        assertTrue(config.isEnabled());
        assertEquals("Custom ${user} Template", config.getTextTemplate());
        assertEquals(300, config.getXSpacing());
        assertEquals(250, config.getYSpacing());
        assertEquals(45, config.getAngle());
        assertEquals(0.5, config.getOpacity());
        assertEquals(24, config.getFontSize());
        assertTrue(config.isAntiCopy());
        assertFalse(config.isApplyToMobile());
    }

    @Test
    void testProcessTemplate_BasicPlaceholders()
    {
        // Setup: Mock user reference
        when(documentAccessBridge.getCurrentUserReference()).thenReturn(userReference);
        when(entityReferenceSerializer.serialize(userReference)).thenReturn("XWiki.TestUser");

        // Test user placeholder
        String result = watermarkService.processTemplate("Hello ${user}!");
        assertEquals("Hello TestUser!", result);

        // Test timestamp placeholder (should contain current date/time)
        result = watermarkService.processTemplate("Time: ${timestamp}");
        assertTrue(result.startsWith("Time: "));
        assertTrue(result.contains("-")); // Date separator
        assertTrue(result.contains(":")); // Time separator

        // Test date placeholder
        result = watermarkService.processTemplate("Date: ${date}");
        assertTrue(result.startsWith("Date: "));
        assertTrue(result.matches("Date: \\d{4}-\\d{2}-\\d{2}"));

        // Test time placeholder
        result = watermarkService.processTemplate("Time: ${time}");
        assertTrue(result.startsWith("Time: "));
        assertTrue(result.matches("Time: \\d{2}:\\d{2}:\\d{2}"));
    }

    @Test
    void testProcessTemplate_MultiplePlaceholders()
    {
        // Setup: Mock user reference
        when(documentAccessBridge.getCurrentUserReference()).thenReturn(userReference);
        when(entityReferenceSerializer.serialize(userReference)).thenReturn("XWiki.TestUser");

        // Test multiple placeholders
        String result = watermarkService.processTemplate("User: ${user}, Date: ${date}, Time: ${time}");
        
        assertTrue(result.contains("User: TestUser"));
        assertTrue(result.contains("Date: "));
        assertTrue(result.contains("Time: "));
    }

    @Test
    void testProcessTemplate_UnknownPlaceholder()
    {
        // Test unknown placeholder (should be preserved)
        String result = watermarkService.processTemplate("Unknown: ${unknown}");
        assertEquals("Unknown: ${unknown}", result);
    }

    @Test
    void testProcessTemplate_NullInput()
    {
        // Test null input
        assertThrows(IllegalArgumentException.class, () -> {
            watermarkService.processTemplate(null);
        });
    }

    @Test
    void testProcessTemplate_EmptyInput()
    {
        // Test empty input
        String result = watermarkService.processTemplate("");
        assertEquals("", result);
    }

    @Test
    void testIsEnabled_True()
    {
        // Setup: Mock enabled configuration
        when(configurationSource.getProperty("watermark.enabled", false)).thenReturn(true);
        mockDefaultConfiguration();

        // Execute and verify
        assertTrue(watermarkService.isEnabled());
    }

    @Test
    void testIsEnabled_False()
    {
        // Setup: Mock disabled configuration
        when(configurationSource.getProperty("watermark.enabled", false)).thenReturn(false);
        mockDefaultConfiguration();

        // Execute and verify
        assertFalse(watermarkService.isEnabled());
    }

    @Test
    void testGetCurrentUserDisplayName_WithUser()
    {
        // Setup: Mock user reference
        when(documentAccessBridge.getCurrentUserReference()).thenReturn(userReference);
        when(entityReferenceSerializer.serialize(userReference)).thenReturn("XWiki.TestUser");

        // Execute and verify
        String displayName = watermarkService.getCurrentUserDisplayName();
        assertEquals("TestUser", displayName);
    }

    @Test
    void testGetCurrentUserDisplayName_NoUser()
    {
        // Setup: Mock no user
        when(documentAccessBridge.getCurrentUserReference()).thenReturn(null);

        // Execute and verify
        String displayName = watermarkService.getCurrentUserDisplayName();
        assertEquals("Guest", displayName);
    }

    @Test
    void testGetCurrentUserDisplayName_Exception()
    {
        // Setup: Mock exception
        when(documentAccessBridge.getCurrentUserReference()).thenThrow(new RuntimeException("Test exception"));

        // Execute and verify
        String displayName = watermarkService.getCurrentUserDisplayName();
        assertEquals("Guest", displayName);
    }

    @Test
    void testRefreshConfiguration()
    {
        // Setup: Get initial configuration
        mockDefaultConfiguration();
        WatermarkConfig initialConfig = watermarkService.getConfiguration();
        assertNotNull(initialConfig);

        // Execute: Refresh configuration
        watermarkService.refreshConfiguration();

        // Verify: Configuration should be reloaded on next access
        // (This is more of an integration test, but we can verify the method doesn't throw)
        assertDoesNotThrow(() -> watermarkService.getConfiguration());
    }

    /**
     * Helper method to mock default configuration values.
     */
    private void mockDefaultConfiguration()
    {
        when(configurationSource.getProperty("watermark.textTemplate", WatermarkConfig.DEFAULT_TEXT_TEMPLATE))
            .thenReturn(WatermarkConfig.DEFAULT_TEXT_TEMPLATE);
        when(configurationSource.getProperty("watermark.xSpacing", WatermarkConfig.DEFAULT_X_SPACING))
            .thenReturn(WatermarkConfig.DEFAULT_X_SPACING);
        when(configurationSource.getProperty("watermark.ySpacing", WatermarkConfig.DEFAULT_Y_SPACING))
            .thenReturn(WatermarkConfig.DEFAULT_Y_SPACING);
        when(configurationSource.getProperty("watermark.angle", WatermarkConfig.DEFAULT_ANGLE))
            .thenReturn(WatermarkConfig.DEFAULT_ANGLE);
        when(configurationSource.getProperty("watermark.opacity", WatermarkConfig.DEFAULT_OPACITY))
            .thenReturn(WatermarkConfig.DEFAULT_OPACITY);
        when(configurationSource.getProperty("watermark.fontSize", WatermarkConfig.DEFAULT_FONT_SIZE))
            .thenReturn(WatermarkConfig.DEFAULT_FONT_SIZE);
        when(configurationSource.getProperty("watermark.antiCopy", false)).thenReturn(false);
        when(configurationSource.getProperty("watermark.applyToMobile", true)).thenReturn(true);
    }
}
